"use client"

// Basit bir veritabanı simülasyonu
// Gerçek bir veritabanı kullanmak için bu dosyayı değiştirin

import { useState, useEffect } from "react"

// Veri tipleri
export interface Urun {
  id: number
  ad: string
  marka?: string
  model?: string
  kategori: string
  alisFiyati: number
  satisFiyati: number
  stok: number
  ozellikler?: string
  resimUrl: string
}

export interface Islem {
  id: number
  tarih: Date
  kategori: string
  urunId: number
  urunAdi: string
  alisFiyati: number
  satisFiyati: number
  kar: number
  musteriAdi: string
}

// LocalStorage anahtarları
const URUNLER_KEY = "telefoncu_urunler"
const ISLEMLER_KEY = "telefoncu_islemler"

// Örnek ürün verileri
const ornekUrunler: Urun[] = [
  // Telefonlar
  {
    id: 1,
    ad: "iPhone 13",
    marka: "Apple",
    model: "iPhone 13",
    kategori: "Telefon",
    alisFiyati: 18000,
    satisFiyati: 22000,
    stok: 5,
    ozellikler: "128GB, Siyah",
    resimUrl: "/placeholder.svg?height=80&width=80",
  },
  {
    id: 2,
    ad: "iPhone 13 Pro",
    marka: "Apple",
    model: "iPhone 13 Pro",
    kategori: "Telefon",
    alisFiyati: 25000,
    satisFiyati: 30000,
    stok: 3,
    ozellikler: "256GB, Mavi",
    resimUrl: "/placeholder.svg?height=80&width=80",
  },
  {
    id: 3,
    ad: "Galaxy S22",
    marka: "Samsung",
    model: "Galaxy S22",
    kategori: "Telefon",
    alisFiyati: 15000,
    satisFiyati: 18500,
    stok: 7,
    ozellikler: "128GB, Beyaz",
    resimUrl: "/placeholder.svg?height=80&width=80",
  },
  {
    id: 4,
    ad: "Galaxy S22 Ultra",
    marka: "Samsung",
    model: "Galaxy S22 Ultra",
    kategori: "Telefon",
    alisFiyati: 22000,
    satisFiyati: 26000,
    stok: 2,
    ozellikler: "256GB, Siyah",
    resimUrl: "/placeholder.svg?height=80&width=80",
  },
  {
    id: 5,
    ad: "Redmi Note 11",
    marka: "Xiaomi",
    model: "Redmi Note 11",
    kategori: "Telefon",
    alisFiyati: 6000,
    satisFiyati: 7500,
    stok: 10,
    ozellikler: "128GB, Gri",
    resimUrl: "/placeholder.svg?height=80&width=80",
  },
  // Aksesuarlar
  {
    id: 6,
    ad: "iPhone 13 Silikon Kılıf",
    kategori: "Aksesuar",
    alisFiyati: 50,
    satisFiyati: 150,
    stok: 25,
    resimUrl: "/placeholder.svg?height=80&width=80",
  },
  {
    id: 7,
    ad: "Samsung S22 Şeffaf Kılıf",
    kategori: "Aksesuar",
    alisFiyati: 40,
    satisFiyati: 120,
    stok: 15,
    resimUrl: "/placeholder.svg?height=80&width=80",
  },
  {
    id: 8,
    ad: "Type-C Şarj Kablosu",
    kategori: "Aksesuar",
    alisFiyati: 30,
    satisFiyati: 90,
    stok: 20,
    resimUrl: "/placeholder.svg?height=80&width=80",
  },
  {
    id: 9,
    ad: "iPhone Lightning Şarj Kablosu",
    kategori: "Aksesuar",
    alisFiyati: 35,
    satisFiyati: 100,
    stok: 18,
    resimUrl: "/placeholder.svg?height=80&width=80",
  },
  {
    id: 10,
    ad: "Bluetooth Kulaklık",
    kategori: "Aksesuar",
    alisFiyati: 150,
    satisFiyati: 300,
    stok: 10,
    resimUrl: "/placeholder.svg?height=80&width=80",
  },
]

// Örnek işlem verileri
const ornekIslemler: Islem[] = [
  {
    id: 1,
    tarih: new Date("2025-05-17"),
    kategori: "Telefon",
    urunId: 1,
    urunAdi: "iPhone 13",
    alisFiyati: 18000,
    satisFiyati: 22000,
    kar: 4000,
    musteriAdi: "Ahmet Yılmaz",
  },
  {
    id: 2,
    tarih: new Date("2025-05-16"),
    kategori: "Aksesuar",
    urunId: 6,
    urunAdi: "iPhone 13 Silikon Kılıf",
    alisFiyati: 50,
    satisFiyati: 150,
    kar: 100,
    musteriAdi: "Mehmet Demir",
  },
]

// Veritabanı işlemleri
export function useUrunler() {
  const [urunler, setUrunler] = useState<Urun[]>([])
  const [yukleniyor, setYukleniyor] = useState(true)

  // Ürünleri localStorage'dan yükle
  useEffect(() => {
    const storedUrunler = localStorage.getItem(URUNLER_KEY)
    if (storedUrunler) {
      setUrunler(JSON.parse(storedUrunler))
    } else {
      // İlk kez çalıştırılıyorsa örnek verileri kullan
      setUrunler(ornekUrunler)
      localStorage.setItem(URUNLER_KEY, JSON.stringify(ornekUrunler))
    }
    setYukleniyor(false)
  }, [])

  // Ürün ekle
  const urunEkle = (urun: Omit<Urun, "id">) => {
    const yeniUrun = {
      ...urun,
      id: urunler.length > 0 ? Math.max(...urunler.map((u) => u.id)) + 1 : 1,
    }
    const yeniUrunler = [...urunler, yeniUrun]
    setUrunler(yeniUrunler)
    localStorage.setItem(URUNLER_KEY, JSON.stringify(yeniUrunler))
    return yeniUrun
  }

  // Ürün güncelle
  const urunGuncelle = (id: number, guncelUrun: Partial<Urun>) => {
    const yeniUrunler = urunler.map((urun) => (urun.id === id ? { ...urun, ...guncelUrun } : urun))
    setUrunler(yeniUrunler)
    localStorage.setItem(URUNLER_KEY, JSON.stringify(yeniUrunler))
  }

  // Ürün sil
  const urunSil = (id: number) => {
    const yeniUrunler = urunler.filter((urun) => urun.id !== id)
    setUrunler(yeniUrunler)
    localStorage.setItem(URUNLER_KEY, JSON.stringify(yeniUrunler))
  }

  // Stok güncelle
  const stokGuncelle = (id: number, miktar: number) => {
    const urun = urunler.find((u) => u.id === id)
    if (urun) {
      const yeniStok = urun.stok + miktar
      if (yeniStok >= 0) {
        urunGuncelle(id, { stok: yeniStok })
        return true
      }
    }
    return false
  }

  return { urunler, yukleniyor, urunEkle, urunGuncelle, urunSil, stokGuncelle }
}

export function useIslemler() {
  const [islemler, setIslemler] = useState<Islem[]>([])
  const [yukleniyor, setYukleniyor] = useState(true)
  const { stokGuncelle } = useUrunler()

  // İşlemleri localStorage'dan yükle
  useEffect(() => {
    const storedIslemler = localStorage.getItem(ISLEMLER_KEY)
    if (storedIslemler) {
      // Tarih string olarak saklandığı için Date nesnesine çevir
      const parsedIslemler = JSON.parse(storedIslemler).map((islem: any) => ({
        ...islem,
        tarih: new Date(islem.tarih),
      }))
      setIslemler(parsedIslemler)
    } else {
      // İlk kez çalıştırılıyorsa örnek verileri kullan
      setIslemler(ornekIslemler)
      localStorage.setItem(ISLEMLER_KEY, JSON.stringify(ornekIslemler))
    }
    setYukleniyor(false)
  }, [])

  // İşlem ekle
  const islemEkle = (islem: Omit<Islem, "id">) => {
    const yeniIslem = {
      ...islem,
      id: islemler.length > 0 ? Math.max(...islemler.map((i) => i.id)) + 1 : 1,
    }
    const yeniIslemler = [...islemler, yeniIslem]
    setIslemler(yeniIslemler)
    localStorage.setItem(ISLEMLER_KEY, JSON.stringify(yeniIslemler))
    return yeniIslem
  }

  // İşlem sil
  const islemSil = (id: number) => {
    // Silinecek işlemi bul
    const silinecekIslem = islemler.find((islem) => islem.id === id)

    if (silinecekIslem) {
      // Eğer işlem bir ürüne bağlıysa, stok miktarını geri al
      if (silinecekIslem.urunId > 0) {
        // Stok miktarını +1 arttır (satışı geri al)
        stokGuncelle(silinecekIslem.urunId, 1)
      }

      // İşlemi sil
      const yeniIslemler = islemler.filter((islem) => islem.id !== id)
      setIslemler(yeniIslemler)
      localStorage.setItem(ISLEMLER_KEY, JSON.stringify(yeniIslemler))
      return true
    }

    return false
  }

  // İşlem güncelle
  const islemGuncelle = (id: number, guncelIslem: Partial<Islem>) => {
    // Güncellenecek işlemi bul
    const eskiIslem = islemler.find((islem) => islem.id === id)

    if (eskiIslem) {
      // Eğer ürün ID'si değiştiyse veya ürün ID'si varsa stok güncelle
      if (guncelIslem.urunId !== undefined && eskiIslem.urunId !== guncelIslem.urunId) {
        // Eski ürünün stokunu geri al
        if (eskiIslem.urunId > 0) {
          stokGuncelle(eskiIslem.urunId, 1)
        }

        // Yeni ürünün stokunu düş
        if (guncelIslem.urunId > 0) {
          stokGuncelle(guncelIslem.urunId, -1)
        }
      }

      // Kar hesapla (eğer alış veya satış fiyatı değiştiyse)
      let yeniKar = eskiIslem.kar
      if (guncelIslem.alisFiyati !== undefined || guncelIslem.satisFiyati !== undefined) {
        const alisFiyati = guncelIslem.alisFiyati !== undefined ? guncelIslem.alisFiyati : eskiIslem.alisFiyati
        const satisFiyati = guncelIslem.satisFiyati !== undefined ? guncelIslem.satisFiyati : eskiIslem.satisFiyati
        yeniKar = satisFiyati - alisFiyati
      }

      // İşlemi güncelle
      const yeniIslemler = islemler.map((islem) =>
        islem.id === id
          ? { ...islem, ...guncelIslem, kar: guncelIslem.kar !== undefined ? guncelIslem.kar : yeniKar }
          : islem,
      )

      setIslemler(yeniIslemler)
      localStorage.setItem(ISLEMLER_KEY, JSON.stringify(yeniIslemler))
      return true
    }

    return false
  }

  return { islemler, yukleniyor, islemEkle, islemSil, islemGuncelle }
}

// Rapor oluşturma
export function raporOlustur(islemler: Islem[], baslangicTarihi?: Date, bitisTarihi?: Date) {
  // Tarih filtreleme
  let filtrelenmisIslemler = [...islemler]
  if (baslangicTarihi) {
    filtrelenmisIslemler = filtrelenmisIslemler.filter((islem) => islem.tarih >= baslangicTarihi)
  }
  if (bitisTarihi) {
    filtrelenmisIslemler = filtrelenmisIslemler.filter((islem) => islem.tarih <= bitisTarihi)
  }

  // Toplam değerler
  const toplamIslem = filtrelenmisIslemler.length
  const toplamMaliyet = filtrelenmisIslemler.reduce((sum, islem) => sum + islem.alisFiyati, 0)
  const toplamSatis = filtrelenmisIslemler.reduce((sum, islem) => sum + islem.satisFiyati, 0)
  const toplamKar = filtrelenmisIslemler.reduce((sum, islem) => sum + islem.kar, 0)
  const karMarji = toplamSatis > 0 ? (toplamKar / toplamSatis) * 100 : 0

  // Kategori bazlı özet
  const kategoriBazli = filtrelenmisIslemler.reduce(
    (acc, islem) => {
      if (!acc[islem.kategori]) {
        acc[islem.kategori] = {
          islemSayisi: 0,
          toplamMaliyet: 0,
          toplamSatis: 0,
          toplamKar: 0,
        }
      }

      acc[islem.kategori].islemSayisi += 1
      acc[islem.kategori].toplamMaliyet += islem.alisFiyati
      acc[islem.kategori].toplamSatis += islem.satisFiyati
      acc[islem.kategori].toplamKar += islem.kar

      return acc
    },
    {} as Record<string, { islemSayisi: number; toplamMaliyet: number; toplamSatis: number; toplamKar: number }>,
  )

  // Günlük özet
  const gunlukOzet = filtrelenmisIslemler.reduce(
    (acc, islem) => {
      const tarihStr = islem.tarih.toISOString().split("T")[0]

      if (!acc[tarihStr]) {
        acc[tarihStr] = {
          tarih: islem.tarih,
          islemSayisi: 0,
          toplamMaliyet: 0,
          toplamSatis: 0,
          toplamKar: 0,
        }
      }

      acc[tarihStr].islemSayisi += 1
      acc[tarihStr].toplamMaliyet += islem.alisFiyati
      acc[tarihStr].toplamSatis += islem.satisFiyati
      acc[tarihStr].toplamKar += islem.kar

      return acc
    },
    {} as Record<
      string,
      { tarih: Date; islemSayisi: number; toplamMaliyet: number; toplamSatis: number; toplamKar: number }
    >,
  )

  return {
    toplamIslem,
    toplamMaliyet,
    toplamSatis,
    toplamKar,
    karMarji,
    kategoriBazli,
    gunlukOzet,
    islemler: filtrelenmisIslemler,
  }
}

// CSV formatında rapor indirme
export function raporuIndir(islemler: Islem[], baslangicTarihi?: Date, bitisTarihi?: Date) {
  const rapor = raporOlustur(islemler, baslangicTarihi, bitisTarihi)

  // CSV başlıkları
  let csv = "ID,Tarih,Kategori,Ürün Adı,Alış Fiyatı,Satış Fiyatı,Kar,Müşteri Adı\n"

  // İşlemleri CSV'ye ekle
  rapor.islemler.forEach((islem) => {
    csv += `${islem.id},${islem.tarih.toLocaleDateString()},${islem.kategori},${islem.urunAdi},${islem.alisFiyati},${islem.satisFiyati},${islem.kar},${islem.musteriAdi}\n`
  })

  // Özet bilgileri ekle
  csv += "\nÖzet Bilgiler\n"
  csv += `Toplam İşlem,${rapor.toplamIslem}\n`
  csv += `Toplam Maliyet,${rapor.toplamMaliyet}\n`
  csv += `Toplam Satış,${rapor.toplamSatis}\n`
  csv += `Toplam Kar,${rapor.toplamKar}\n`
  csv += `Kar Marjı,%${rapor.karMarji.toFixed(2)}\n`

  // Kategori bazlı özet
  csv += "\nKategori Bazlı Özet\n"
  csv += "Kategori,İşlem Sayısı,Toplam Maliyet,Toplam Satış,Toplam Kar,Kar Marjı\n"
  Object.entries(rapor.kategoriBazli).forEach(([kategori, data]) => {
    const karMarji = data.toplamSatis > 0 ? (data.toplamKar / data.toplamSatis) * 100 : 0
    csv += `${kategori},${data.islemSayisi},${data.toplamMaliyet},${data.toplamSatis},${data.toplamKar},%${karMarji.toFixed(2)}\n`
  })

  // CSV dosyasını indir
  const blob = new Blob([csv], { type: "text/csv;charset=utf-8;" })
  const url = URL.createObjectURL(blob)
  const link = document.createElement("a")
  link.setAttribute("href", url)
  link.setAttribute("download", `telefoncu_rapor_${new Date().toISOString().split("T")[0]}.csv`)
  link.style.visibility = "hidden"
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}
