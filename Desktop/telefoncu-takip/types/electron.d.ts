// Electron API tipleri
interface ElectronAPI {
  // <PERSON><PERSON>ı
  onMenuAction: (callback: (event: any, action: string) => void) => void;
  removeMenuListeners: () => void;

  // Platform bilgisi
  platform: string;

  // Uygulama bilgileri
  getAppVersion: () => string;

  // Dosya sistemi
  saveFile: (data: any, filename: string) => Promise<any>;
  loadFile: (filename: string) => Promise<any>;

  // Bildirimler
  showNotification: (title: string, body: string) => Promise<any>;

  // Veritabanı API'leri
  db: {
    // Ürün işlemleri
    getAllUrunler: () => Promise<any[]>;
    getUrunById: (id: number) => Promise<any>;
    insertUrun: (urun: any) => Promise<any>;
    updateUrun: (id: number, urun: any) => Promise<boolean>;
    deleteUrun: (id: number) => Promise<boolean>;
    updateStok: (id: number, miktar: number) => Promise<boolean>;

    // İşlem işlemleri
    getAllIslemler: () => Promise<any[]>;
    getIslemById: (id: number) => Promise<any>;
    insertIslem: (islem: any) => Promise<any>;
    updateIslem: (id: number, islem: any) => Promise<boolean>;
    deleteIslem: (id: number) => Promise<boolean>;

    // Müşteri işlemleri
    getAllMusteriler: () => Promise<any[]>;
    getMusteriById: (id: number) => Promise<any>;
    insertMusteri: (musteri: any) => Promise<any>;
    updateMusteri: (id: number, musteri: any) => Promise<boolean>;
    deleteMusteri: (id: number) => Promise<boolean>;

    // Rapor işlemleri
    getRaporData: (baslangicTarihi: string, bitisTarihi: string) => Promise<any>;
  };
}

declare global {
  interface Window {
    electronAPI: ElectronAPI;
  }
}

export {};
