#!/usr/bin/env node

// SQLite Veritabanı Bağlantı ve Yönetim Scripti
const Database = require('better-sqlite3');
const path = require('path');
const fs = require('fs');
const os = require('os');

// Veritabanı yolunu belirle
function getDatabasePath() {
  let userDataPath;
  
  switch (process.platform) {
    case 'darwin': // macOS
      userDataPath = path.join(os.homedir(), 'Library', 'Application Support', 'telefoncu-takip-electron');
      break;
    case 'win32': // Windows
      userDataPath = path.join(os.homedir(), 'AppData', 'Roaming', 'telefoncu-takip-electron');
      break;
    case 'linux': // Linux
      userDataPath = path.join(os.homedir(), '.config', 'telefoncu-takip-electron');
      break;
    default:
      userDataPath = path.join(os.homedir(), '.telefoncu-takip-electron');
  }
  
  return path.join(userDataPath, 'telefoncu-takip.db');
}

// Veritabanı bilgilerini göster
function showDatabaseInfo() {
  const dbPath = getDatabasePath();
  
  console.log('🗄️  SQLite Veritabanı Bağlantı Bilgileri');
  console.log('=' .repeat(50));
  console.log(`📁 Dosya Yolu: ${dbPath}`);
  console.log(`📊 Platform: ${process.platform}`);
  console.log(`✅ Dosya Var: ${fs.existsSync(dbPath) ? 'Evet' : 'Hayır'}`);
  
  if (fs.existsSync(dbPath)) {
    const stats = fs.statSync(dbPath);
    console.log(`📏 Dosya Boyutu: ${(stats.size / 1024).toFixed(2)} KB`);
    console.log(`📅 Son Değişiklik: ${stats.mtime.toLocaleString()}`);
  }
  
  console.log('\n🔧 Bağlantı Komutları:');
  console.log(`sqlite3 "${dbPath}"`);
  console.log(`sqlite3 "${dbPath}" ".tables"`);
  console.log(`sqlite3 "${dbPath}" ".schema"`);
  
  return dbPath;
}

// Veritabanı istatistiklerini göster
function showDatabaseStats(dbPath) {
  try {
    const db = new Database(dbPath, { readonly: true });
    
    console.log('\n📊 Veritabanı İstatistikleri:');
    console.log('-'.repeat(30));
    
    // Tablo sayıları
    const urunSayisi = db.prepare('SELECT COUNT(*) as count FROM urunler').get();
    const islemSayisi = db.prepare('SELECT COUNT(*) as count FROM islemler').get();
    const musteriSayisi = db.prepare('SELECT COUNT(*) as count FROM musteriler').get();
    
    console.log(`📦 Ürün Sayısı: ${urunSayisi.count}`);
    console.log(`💰 İşlem Sayısı: ${islemSayisi.count}`);
    console.log(`👥 Müşteri Sayısı: ${musteriSayisi.count}`);
    
    // Son işlemler
    if (islemSayisi.count > 0) {
      console.log('\n💼 Son 3 İşlem:');
      const sonIslemler = db.prepare(`
        SELECT id, tarih, kategori, urun_adi, satis_fiyati, musteri_adi 
        FROM islemler 
        ORDER BY id DESC 
        LIMIT 3
      `).all();
      
      sonIslemler.forEach(islem => {
        console.log(`  ${islem.id}: ${islem.urun_adi} - ${islem.satis_fiyati}₺ (${islem.musteri_adi})`);
      });
    }
    
    // Stok durumu
    console.log('\n📦 Kritik Stok (≤5):');
    const kritikStok = db.prepare(`
      SELECT ad, stok, kategori 
      FROM urunler 
      WHERE stok <= 5 
      ORDER BY stok ASC
    `).all();
    
    if (kritikStok.length > 0) {
      kritikStok.forEach(urun => {
        console.log(`  ⚠️  ${urun.ad}: ${urun.stok} adet`);
      });
    } else {
      console.log('  ✅ Tüm ürünler yeterli stokta');
    }
    
    db.close();
  } catch (error) {
    console.error('❌ Veritabanı okuma hatası:', error.message);
  }
}

// SQL sorgusu çalıştır
function runQuery(dbPath, query) {
  try {
    const db = new Database(dbPath, { readonly: true });
    
    console.log(`\n🔍 Sorgu: ${query}`);
    console.log('-'.repeat(50));
    
    if (query.toLowerCase().startsWith('select')) {
      const results = db.prepare(query).all();
      
      if (results.length > 0) {
        console.table(results);
      } else {
        console.log('📭 Sonuç bulunamadı');
      }
    } else {
      console.log('⚠️  Güvenlik nedeniyle sadece SELECT sorguları desteklenir');
    }
    
    db.close();
  } catch (error) {
    console.error('❌ Sorgu hatası:', error.message);
  }
}

// Yedekleme
function backupDatabase(dbPath) {
  try {
    const backupPath = dbPath.replace('.db', `_backup_${Date.now()}.db`);
    fs.copyFileSync(dbPath, backupPath);
    console.log(`✅ Yedek oluşturuldu: ${backupPath}`);
  } catch (error) {
    console.error('❌ Yedekleme hatası:', error.message);
  }
}

// Ana fonksiyon
function main() {
  const args = process.argv.slice(2);
  const command = args[0];
  
  const dbPath = showDatabaseInfo();
  
  if (!fs.existsSync(dbPath)) {
    console.log('\n❌ Veritabanı dosyası bulunamadı!');
    console.log('💡 Önce Electron uygulamasını çalıştırın: npm run electron');
    return;
  }
  
  switch (command) {
    case 'stats':
      showDatabaseStats(dbPath);
      break;
      
    case 'query':
      const query = args[1];
      if (query) {
        runQuery(dbPath, query);
      } else {
        console.log('\n❌ Sorgu belirtilmedi!');
        console.log('💡 Kullanım: node db-connect.js query "SELECT * FROM urunler"');
      }
      break;
      
    case 'backup':
      backupDatabase(dbPath);
      break;
      
    case 'shell':
      console.log('\n🐚 SQLite shell başlatılıyor...');
      const { spawn } = require('child_process');
      const sqlite = spawn('sqlite3', [dbPath], { stdio: 'inherit' });
      break;
      
    default:
      showDatabaseStats(dbPath);
      console.log('\n🔧 Kullanılabilir Komutlar:');
      console.log('  node db-connect.js stats     - İstatistikleri göster');
      console.log('  node db-connect.js query "SQL" - Sorgu çalıştır');
      console.log('  node db-connect.js backup    - Yedek oluştur');
      console.log('  node db-connect.js shell     - SQLite shell aç');
  }
}

// Scripti çalıştır
if (require.main === module) {
  main();
}

module.exports = { getDatabasePath, showDatabaseInfo, showDatabaseStats };
