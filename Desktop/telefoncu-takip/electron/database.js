const Database = require('better-sqlite3');
const path = require('path');
const fs = require('fs');
const { app } = require('electron');

class DatabaseManager {
  constructor() {
    this.db = null;
    this.dbPath = null;
  }

  // Veritabanını başlat
  initialize() {
    try {
      // Veritabanı dosyasının konumunu belirle
      const userDataPath = app.getPath('userData');
      this.dbPath = path.join(userDataPath, 'telefoncu-takip.db');
      
      console.log('Database path:', this.dbPath);
      
      // Veritabanını aç
      this.db = new Database(this.dbPath);
      
      // WAL modunu etkinleştir (performans için)
      this.db.pragma('journal_mode = WAL');
      
      // Tabloları oluştur
      this.createTables();
      
      // Örnek verileri ekle (eğer tablolar boşsa)
      this.insertSampleData();
      
      console.log('Database initialized successfully');
      return true;
    } catch (error) {
      console.error('Database initialization failed:', error);
      return false;
    }
  }

  // Tabloları oluştur
  createTables() {
    // Ürünler tablosu
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS urunler (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        ad TEXT NOT NULL,
        marka TEXT,
        model TEXT,
        kategori TEXT NOT NULL,
        alis_fiyati REAL NOT NULL,
        satis_fiyati REAL NOT NULL,
        stok INTEGER NOT NULL DEFAULT 0,
        ozellikler TEXT,
        resim_url TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // İşlemler tablosu
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS islemler (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        tarih DATETIME NOT NULL,
        kategori TEXT NOT NULL,
        urun_id INTEGER,
        urun_adi TEXT NOT NULL,
        alis_fiyati REAL NOT NULL,
        satis_fiyati REAL NOT NULL,
        kar REAL NOT NULL,
        musteri_adi TEXT NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (urun_id) REFERENCES urunler (id)
      )
    `);

    // Müşteriler tablosu
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS musteriler (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        ad TEXT NOT NULL,
        telefon TEXT,
        email TEXT,
        adres TEXT,
        notlar TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Trigger'lar - updated_at otomatik güncelleme
    this.db.exec(`
      CREATE TRIGGER IF NOT EXISTS update_urunler_timestamp 
      AFTER UPDATE ON urunler
      BEGIN
        UPDATE urunler SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
      END
    `);

    this.db.exec(`
      CREATE TRIGGER IF NOT EXISTS update_musteriler_timestamp 
      AFTER UPDATE ON musteriler
      BEGIN
        UPDATE musteriler SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
      END
    `);
  }

  // Örnek verileri ekle
  insertSampleData() {
    // Ürün sayısını kontrol et
    const urunSayisi = this.db.prepare('SELECT COUNT(*) as count FROM urunler').get();
    
    if (urunSayisi.count === 0) {
      console.log('Inserting sample products...');
      
      const insertUrun = this.db.prepare(`
        INSERT INTO urunler (ad, marka, model, kategori, alis_fiyati, satis_fiyati, stok, ozellikler, resim_url)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
      `);

      const ornekUrunler = [
        ['iPhone 13', 'Apple', 'iPhone 13', 'Telefon', 18000, 22000, 5, '128GB, Siyah', '/placeholder.svg?height=80&width=80'],
        ['iPhone 13 Pro', 'Apple', 'iPhone 13 Pro', 'Telefon', 25000, 30000, 3, '256GB, Mavi', '/placeholder.svg?height=80&width=80'],
        ['Galaxy S22', 'Samsung', 'Galaxy S22', 'Telefon', 15000, 18500, 7, '128GB, Beyaz', '/placeholder.svg?height=80&width=80'],
        ['Galaxy S22 Ultra', 'Samsung', 'Galaxy S22 Ultra', 'Telefon', 22000, 26000, 2, '256GB, Siyah', '/placeholder.svg?height=80&width=80'],
        ['Redmi Note 11', 'Xiaomi', 'Redmi Note 11', 'Telefon', 6000, 7500, 10, '128GB, Gri', '/placeholder.svg?height=80&width=80'],
        ['iPhone 13 Silikon Kılıf', null, null, 'Aksesuar', 50, 150, 25, null, '/placeholder.svg?height=80&width=80'],
        ['Samsung S22 Şeffaf Kılıf', null, null, 'Aksesuar', 40, 120, 15, null, '/placeholder.svg?height=80&width=80'],
        ['Type-C Şarj Kablosu', null, null, 'Aksesuar', 30, 90, 20, null, '/placeholder.svg?height=80&width=80'],
        ['iPhone Lightning Şarj Kablosu', null, null, 'Aksesuar', 35, 100, 18, null, '/placeholder.svg?height=80&width=80'],
        ['Bluetooth Kulaklık', null, null, 'Aksesuar', 150, 300, 10, null, '/placeholder.svg?height=80&width=80']
      ];

      const transaction = this.db.transaction((urunler) => {
        for (const urun of urunler) {
          insertUrun.run(...urun);
        }
      });

      transaction(ornekUrunler);
    }

    // İşlem sayısını kontrol et
    const islemSayisi = this.db.prepare('SELECT COUNT(*) as count FROM islemler').get();
    
    if (islemSayisi.count === 0) {
      console.log('Inserting sample transactions...');
      
      const insertIslem = this.db.prepare(`
        INSERT INTO islemler (tarih, kategori, urun_id, urun_adi, alis_fiyati, satis_fiyati, kar, musteri_adi)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `);

      const ornekIslemler = [
        ['2025-05-17', 'Telefon', 1, 'iPhone 13', 18000, 22000, 4000, 'Ahmet Yılmaz'],
        ['2025-05-16', 'Aksesuar', 6, 'iPhone 13 Silikon Kılıf', 50, 150, 100, 'Mehmet Demir']
      ];

      const transaction = this.db.transaction((islemler) => {
        for (const islem of islemler) {
          insertIslem.run(...islem);
        }
      });

      transaction(ornekIslemler);
    }
  }

  // Veritabanını kapat
  close() {
    if (this.db) {
      this.db.close();
      this.db = null;
      console.log('Database closed');
    }
  }

  // Veritabanı referansını al
  getDatabase() {
    return this.db;
  }

  // Veritabanı yolunu al
  getDatabasePath() {
    return this.dbPath;
  }
}

module.exports = DatabaseManager;
