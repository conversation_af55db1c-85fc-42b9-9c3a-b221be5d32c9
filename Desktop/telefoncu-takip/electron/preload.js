const { contextBridge, ipcRenderer } = require('electron');

// Güvenli API'leri renderer sürecine maruz bırak
contextBridge.exposeInMainWorld('electronAPI', {
  // <PERSON><PERSON> olaylarını dinle
  onMenuAction: (callback) => {
    ipcRenderer.on('new-transaction', callback);
    ipcRenderer.on('generate-report', callback);
  },
  
  // Menü olay dinleyicilerini kaldır
  removeMenuListeners: () => {
    ipcRenderer.removeAllListeners('new-transaction');
    ipcRenderer.removeAllListeners('generate-report');
  },

  // Platform bilgisi
  platform: process.platform,
  
  // Uygulama bilgileri
  getAppVersion: () => {
    return process.env.npm_package_version || '0.1.0';
  },

  // Dosya sistemi işlemleri (gelecekte kullanım için)
  saveFile: (data, filename) => {
    return ipcRenderer.invoke('save-file', data, filename);
  },
  
  loadFile: (filename) => {
    return ipcRenderer.invoke('load-file', filename);
  },

  // Bildirim gönder
  showNotification: (title, body) => {
    return ipcRenderer.invoke('show-notification', title, body);
  }
});

// Konsol loglarını ana sürece gönder (geliştirme için)
if (process.env.NODE_ENV === 'development') {
  window.addEventListener('DOMContentLoaded', () => {
    console.log('Electron preload script loaded');
  });
}
