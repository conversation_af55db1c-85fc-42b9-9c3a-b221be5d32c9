// Veritabanı API fonksiyonları
class DatabaseAPI {
  constructor(db) {
    this.db = db;
    this.prepareStatements();
  }

  // Prepared statement'ları hazırla
  prepareStatements() {
    // Ürün sorguları
    this.statements = {
      // Ürünler
      getAllUrunler: this.db.prepare('SELECT * FROM urunler ORDER BY id DESC'),
      getUrunById: this.db.prepare('SELECT * FROM urunler WHERE id = ?'),
      insertUrun: this.db.prepare(`
        INSERT INTO urunler (ad, marka, model, kategori, alis_fiyati, satis_fiyati, stok, ozellikler, resim_url)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
      `),
      updateUrun: this.db.prepare(`
        UPDATE urunler 
        SET ad = ?, marka = ?, model = ?, kategori = ?, alis_fiyati = ?, satis_fiyati = ?, stok = ?, ozellikler = ?, resim_url = ?
        WHERE id = ?
      `),
      deleteUrun: this.db.prepare('DELETE FROM urunler WHERE id = ?'),
      updateStok: this.db.prepare('UPDATE urunler SET stok = stok + ? WHERE id = ?'),

      // İşlemler
      getAllIslemler: this.db.prepare('SELECT * FROM islemler ORDER BY tarih DESC, id DESC'),
      getIslemById: this.db.prepare('SELECT * FROM islemler WHERE id = ?'),
      insertIslem: this.db.prepare(`
        INSERT INTO islemler (tarih, kategori, urun_id, urun_adi, alis_fiyati, satis_fiyati, kar, musteri_adi)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `),
      updateIslem: this.db.prepare(`
        UPDATE islemler 
        SET tarih = ?, kategori = ?, urun_id = ?, urun_adi = ?, alis_fiyati = ?, satis_fiyati = ?, kar = ?, musteri_adi = ?
        WHERE id = ?
      `),
      deleteIslem: this.db.prepare('DELETE FROM islemler WHERE id = ?'),

      // Müşteriler
      getAllMusteriler: this.db.prepare('SELECT * FROM musteriler ORDER BY ad'),
      getMusteriById: this.db.prepare('SELECT * FROM musteriler WHERE id = ?'),
      insertMusteri: this.db.prepare(`
        INSERT INTO musteriler (ad, telefon, email, adres, notlar)
        VALUES (?, ?, ?, ?, ?)
      `),
      updateMusteri: this.db.prepare(`
        UPDATE musteriler 
        SET ad = ?, telefon = ?, email = ?, adres = ?, notlar = ?
        WHERE id = ?
      `),
      deleteMusteri: this.db.prepare('DELETE FROM musteriler WHERE id = ?'),

      // Raporlar
      getIslemlerByDateRange: this.db.prepare(`
        SELECT * FROM islemler 
        WHERE tarih BETWEEN ? AND ? 
        ORDER BY tarih DESC
      `),
      getToplamlar: this.db.prepare(`
        SELECT 
          COUNT(*) as toplam_islem,
          SUM(alis_fiyati) as toplam_maliyet,
          SUM(satis_fiyati) as toplam_satis,
          SUM(kar) as toplam_kar
        FROM islemler
        WHERE tarih BETWEEN ? AND ?
      `),
      getKategoriBazliOzet: this.db.prepare(`
        SELECT 
          kategori,
          COUNT(*) as islem_sayisi,
          SUM(alis_fiyati) as toplam_maliyet,
          SUM(satis_fiyati) as toplam_satis,
          SUM(kar) as toplam_kar
        FROM islemler
        WHERE tarih BETWEEN ? AND ?
        GROUP BY kategori
      `)
    };
  }

  // Ürün işlemleri
  getAllUrunler() {
    try {
      return this.statements.getAllUrunler.all();
    } catch (error) {
      console.error('Error getting all products:', error);
      throw error;
    }
  }

  getUrunById(id) {
    try {
      return this.statements.getUrunById.get(id);
    } catch (error) {
      console.error('Error getting product by id:', error);
      throw error;
    }
  }

  insertUrun(urun) {
    try {
      const result = this.statements.insertUrun.run(
        urun.ad, urun.marka, urun.model, urun.kategori,
        urun.alis_fiyati, urun.satis_fiyati, urun.stok,
        urun.ozellikler, urun.resim_url
      );
      return { id: result.lastInsertRowid, ...urun };
    } catch (error) {
      console.error('Error inserting product:', error);
      throw error;
    }
  }

  updateUrun(id, urun) {
    try {
      const result = this.statements.updateUrun.run(
        urun.ad, urun.marka, urun.model, urun.kategori,
        urun.alis_fiyati, urun.satis_fiyati, urun.stok,
        urun.ozellikler, urun.resim_url, id
      );
      return result.changes > 0;
    } catch (error) {
      console.error('Error updating product:', error);
      throw error;
    }
  }

  deleteUrun(id) {
    try {
      const result = this.statements.deleteUrun.run(id);
      return result.changes > 0;
    } catch (error) {
      console.error('Error deleting product:', error);
      throw error;
    }
  }

  updateStok(id, miktar) {
    try {
      const result = this.statements.updateStok.run(miktar, id);
      return result.changes > 0;
    } catch (error) {
      console.error('Error updating stock:', error);
      throw error;
    }
  }

  // İşlem işlemleri
  getAllIslemler() {
    try {
      return this.statements.getAllIslemler.all();
    } catch (error) {
      console.error('Error getting all transactions:', error);
      throw error;
    }
  }

  getIslemById(id) {
    try {
      return this.statements.getIslemById.get(id);
    } catch (error) {
      console.error('Error getting transaction by id:', error);
      throw error;
    }
  }

  insertIslem(islem) {
    try {
      const result = this.statements.insertIslem.run(
        islem.tarih, islem.kategori, islem.urun_id, islem.urun_adi,
        islem.alis_fiyati, islem.satis_fiyati, islem.kar, islem.musteri_adi
      );
      return { id: result.lastInsertRowid, ...islem };
    } catch (error) {
      console.error('Error inserting transaction:', error);
      throw error;
    }
  }

  updateIslem(id, islem) {
    try {
      const result = this.statements.updateIslem.run(
        islem.tarih, islem.kategori, islem.urun_id, islem.urun_adi,
        islem.alis_fiyati, islem.satis_fiyati, islem.kar, islem.musteri_adi, id
      );
      return result.changes > 0;
    } catch (error) {
      console.error('Error updating transaction:', error);
      throw error;
    }
  }

  deleteIslem(id) {
    try {
      const result = this.statements.deleteIslem.run(id);
      return result.changes > 0;
    } catch (error) {
      console.error('Error deleting transaction:', error);
      throw error;
    }
  }

  // Müşteri işlemleri
  getAllMusteriler() {
    try {
      return this.statements.getAllMusteriler.all();
    } catch (error) {
      console.error('Error getting all customers:', error);
      throw error;
    }
  }

  getMusteriById(id) {
    try {
      return this.statements.getMusteriById.get(id);
    } catch (error) {
      console.error('Error getting customer by id:', error);
      throw error;
    }
  }

  insertMusteri(musteri) {
    try {
      const result = this.statements.insertMusteri.run(
        musteri.ad, musteri.telefon, musteri.email, musteri.adres, musteri.notlar
      );
      return { id: result.lastInsertRowid, ...musteri };
    } catch (error) {
      console.error('Error inserting customer:', error);
      throw error;
    }
  }

  updateMusteri(id, musteri) {
    try {
      const result = this.statements.updateMusteri.run(
        musteri.ad, musteri.telefon, musteri.email, musteri.adres, musteri.notlar, id
      );
      return result.changes > 0;
    } catch (error) {
      console.error('Error updating customer:', error);
      throw error;
    }
  }

  deleteMusteri(id) {
    try {
      const result = this.statements.deleteMusteri.run(id);
      return result.changes > 0;
    } catch (error) {
      console.error('Error deleting customer:', error);
      throw error;
    }
  }

  // Rapor işlemleri
  getRaporData(baslangicTarihi, bitisTarihi) {
    try {
      const islemler = this.statements.getIslemlerByDateRange.all(baslangicTarihi, bitisTarihi);
      const toplamlar = this.statements.getToplamlar.get(baslangicTarihi, bitisTarihi);
      const kategoriBazli = this.statements.getKategoriBazliOzet.all(baslangicTarihi, bitisTarihi);

      return {
        islemler,
        toplamlar,
        kategoriBazli
      };
    } catch (error) {
      console.error('Error getting report data:', error);
      throw error;
    }
  }
}

module.exports = DatabaseAPI;
