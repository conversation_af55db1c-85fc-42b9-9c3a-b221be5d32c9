const { app, BrowserWindow, Menu, dialog, shell, ipcMain } = require('electron');
const path = require('path');
const fs = require('fs');
const DatabaseManager = require('./database');
const DatabaseAPI = require('./database-api');

// Geliştirme modunu kontrol et - out klasörü varsa production
const outPath = path.join(__dirname, '../out/index.html');
const isDev = !fs.existsSync(outPath);

let mainWindow;
let dbManager;
let dbAPI;

function createWindow() {
  // Ana pencereyi oluştur
  mainWindow = new BrowserWindow({
    width: 1400,
    height: 900,
    minWidth: 1200,
    minHeight: 800,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: path.join(__dirname, 'preload.js')
    },
    icon: path.join(__dirname, '../public/placeholder-logo.png'),
    show: true,
    titleBarStyle: process.platform === 'darwin' ? 'hiddenInset' : 'default'
  });

  // Geliştirme modunda DevTools'u aç
  if (isDev) {
    mainWindow.webContents.openDevTools();
  }

  // URL'yi yükle
  if (isDev) {
    mainWindow.loadURL('http://localhost:3000');
  } else {
    mainWindow.loadFile(path.join(__dirname, '../out/index.html'));
  }

  // Pencere kapatıldığında
  mainWindow.on('closed', () => {
    mainWindow = null;
  });

  // Dış bağlantıları varsayılan tarayıcıda aç
  mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    shell.openExternal(url);
    return { action: 'deny' };
  });
}

// Uygulama menüsünü oluştur
function createMenu() {
  const template = [
    {
      label: 'Dosya',
      submenu: [
        {
          label: 'Yeni İşlem',
          accelerator: 'CmdOrCtrl+N',
          click: () => {
            mainWindow.webContents.send('new-transaction');
          }
        },
        {
          label: 'Rapor Al',
          accelerator: 'CmdOrCtrl+R',
          click: () => {
            mainWindow.webContents.send('generate-report');
          }
        },
        { type: 'separator' },
        {
          label: 'Çıkış',
          accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
          click: () => {
            app.quit();
          }
        }
      ]
    },
    {
      label: 'Düzenle',
      submenu: [
        { label: 'Geri Al', accelerator: 'CmdOrCtrl+Z', role: 'undo' },
        { label: 'Yinele', accelerator: 'Shift+CmdOrCtrl+Z', role: 'redo' },
        { type: 'separator' },
        { label: 'Kes', accelerator: 'CmdOrCtrl+X', role: 'cut' },
        { label: 'Kopyala', accelerator: 'CmdOrCtrl+C', role: 'copy' },
        { label: 'Yapıştır', accelerator: 'CmdOrCtrl+V', role: 'paste' }
      ]
    },
    {
      label: 'Görünüm',
      submenu: [
        { label: 'Yeniden Yükle', accelerator: 'CmdOrCtrl+R', role: 'reload' },
        { label: 'Zorla Yeniden Yükle', accelerator: 'CmdOrCtrl+Shift+R', role: 'forceReload' },
        { label: 'Geliştirici Araçları', accelerator: 'F12', role: 'toggleDevTools' },
        { type: 'separator' },
        { label: 'Tam Ekran', accelerator: 'F11', role: 'togglefullscreen' }
      ]
    },
    {
      label: 'Pencere',
      submenu: [
        { label: 'Küçült', accelerator: 'CmdOrCtrl+M', role: 'minimize' },
        { label: 'Kapat', accelerator: 'CmdOrCtrl+W', role: 'close' }
      ]
    },
    {
      label: 'Yardım',
      submenu: [
        {
          label: 'Hakkında',
          click: () => {
            dialog.showMessageBox(mainWindow, {
              type: 'info',
              title: 'Hakkında',
              message: 'Telefoncu Takip Sistemi',
              detail: 'Telefon satış ve servis işlemlerini takip etmek için geliştirilmiş sistem.\n\nSürüm: 0.1.0'
            });
          }
        }
      ]
    }
  ];

  // macOS için menü düzenlemesi
  if (process.platform === 'darwin') {
    template.unshift({
      label: app.getName(),
      submenu: [
        { label: 'Hakkında ' + app.getName(), role: 'about' },
        { type: 'separator' },
        { label: 'Hizmetler', role: 'services', submenu: [] },
        { type: 'separator' },
        { label: app.getName() + ' Gizle', accelerator: 'Command+H', role: 'hide' },
        { label: 'Diğerlerini Gizle', accelerator: 'Command+Shift+H', role: 'hideothers' },
        { label: 'Tümünü Göster', role: 'unhide' },
        { type: 'separator' },
        { label: 'Çıkış', accelerator: 'Command+Q', click: () => app.quit() }
      ]
    });
  }

  const menu = Menu.buildFromTemplate(template);
  Menu.setApplicationMenu(menu);
}

// Veritabanını başlat
function initializeDatabase() {
  dbManager = new DatabaseManager();
  const success = dbManager.initialize();

  if (success) {
    dbAPI = new DatabaseAPI(dbManager.getDatabase());
    setupDatabaseHandlers();
    console.log('Database and API initialized successfully');
  } else {
    console.error('Failed to initialize database');
    dialog.showErrorBox('Veritabanı Hatası', 'Veritabanı başlatılamadı. Uygulama kapatılacak.');
    app.quit();
  }
}

// Veritabanı IPC handler'larını kur
function setupDatabaseHandlers() {
  // Ürün işlemleri
  ipcMain.handle('db:getAllUrunler', async () => {
    return dbAPI.getAllUrunler();
  });

  ipcMain.handle('db:getUrunById', async (event, id) => {
    return dbAPI.getUrunById(id);
  });

  ipcMain.handle('db:insertUrun', async (event, urun) => {
    return dbAPI.insertUrun(urun);
  });

  ipcMain.handle('db:updateUrun', async (event, id, urun) => {
    return dbAPI.updateUrun(id, urun);
  });

  ipcMain.handle('db:deleteUrun', async (event, id) => {
    return dbAPI.deleteUrun(id);
  });

  ipcMain.handle('db:updateStok', async (event, id, miktar) => {
    return dbAPI.updateStok(id, miktar);
  });

  // İşlem işlemleri
  ipcMain.handle('db:getAllIslemler', async () => {
    return dbAPI.getAllIslemler();
  });

  ipcMain.handle('db:getIslemById', async (event, id) => {
    return dbAPI.getIslemById(id);
  });

  ipcMain.handle('db:insertIslem', async (event, islem) => {
    return dbAPI.insertIslem(islem);
  });

  ipcMain.handle('db:updateIslem', async (event, id, islem) => {
    return dbAPI.updateIslem(id, islem);
  });

  ipcMain.handle('db:deleteIslem', async (event, id) => {
    return dbAPI.deleteIslem(id);
  });

  // Müşteri işlemleri
  ipcMain.handle('db:getAllMusteriler', async () => {
    return dbAPI.getAllMusteriler();
  });

  ipcMain.handle('db:getMusteriById', async (event, id) => {
    return dbAPI.getMusteriById(id);
  });

  ipcMain.handle('db:insertMusteri', async (event, musteri) => {
    return dbAPI.insertMusteri(musteri);
  });

  ipcMain.handle('db:updateMusteri', async (event, id, musteri) => {
    return dbAPI.updateMusteri(id, musteri);
  });

  ipcMain.handle('db:deleteMusteri', async (event, id) => {
    return dbAPI.deleteMusteri(id);
  });

  // Rapor işlemleri
  ipcMain.handle('db:getRaporData', async (event, baslangicTarihi, bitisTarihi) => {
    return dbAPI.getRaporData(baslangicTarihi, bitisTarihi);
  });
}

// Uygulama hazır olduğunda
app.whenReady().then(() => {
  initializeDatabase();
  createWindow();
  createMenu();

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow();
    }
  });
});

// Tüm pencereler kapatıldığında
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

// Uygulama kapanmadan önce
app.on('before-quit', () => {
  if (dbManager) {
    dbManager.close();
  }
});

// Güvenlik: Yalnızca güvenli içeriği yükle
app.on('web-contents-created', (event, contents) => {
  contents.on('new-window', (event, navigationUrl) => {
    event.preventDefault();
    shell.openExternal(navigationUrl);
  });
});
