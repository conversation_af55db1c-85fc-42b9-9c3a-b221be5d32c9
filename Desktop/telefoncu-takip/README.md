# Telefoncu Takip Sistemi - Electron Uygulaması

Telefon satış ve servis işlemlerini takip etmek için geliştirilmiş masaüstü uygulaması.

## 🚀 Özellikler

- **Masaüstü Uygulaması**: Electron ile geliştirilmiş, Windows, macOS ve Linux desteği
- **Modern UI**: Next.js ve Tailwind CSS ile responsive tasarım
- **İşlem Takibi**: Satış, alış ve servis işlemlerini kaydetme ve takip etme
- **Stok Yönetimi**: Ürün stoklarını takip etme ve kritik stok uyarıları
- **Müşteri Yönetimi**: Müşteri bilgilerini saklama ve işlem geçmişi
- **Raporlama**: Detaylı satış raporları ve kar-zarar analizi
- **Dashboard**: Özet bilgiler ve grafikler ile işletme durumu
- **<PERSON><PERSON><PERSON>**: Telefon ve aksesuar fiyatlarını düzenleme

## 📋 Gereksinimler

- Node.js 18+ 
- npm veya pnpm

## 🛠️ Kurulum

1. **Projeyi klonlayın:**
```bash
git clone <repository-url>
cd telefoncu-takip
```

2. **Bağımlılıkları yükleyin:**
```bash
npm install --legacy-peer-deps
```

## 🎯 Kullanım

### Geliştirme Modu

Geliştirme modunda hem Next.js hem de Electron'u çalıştırır:

```bash
npm run electron-dev
```

Bu komut:
- Next.js'i localhost:3000'de başlatır
- Next.js hazır olduğunda Electron uygulamasını açar
- Hot reload özelliği ile anlık değişiklikleri gösterir

### Production Build

Uygulamayı production için build etmek:

```bash
npm run build
npm run electron
```

### Dağıtım Paketi Oluşturma

Masaüstü uygulaması olarak paketlemek için:

```bash
npm run dist
```

Bu komut `dist/` klasöründe platform-specific kurulum dosyaları oluşturur:
- **Windows**: `.exe` installer
- **macOS**: `.dmg` dosyası
- **Linux**: `.AppImage` dosyası

## 📁 Proje Yapısı

```
telefoncu-takip/
├── app/                    # Next.js app router sayfaları
│   ├── dashboard/         # Yönetim paneli
│   ├── musteri/          # Müşteri yönetimi
│   ├── stok/             # Stok takibi
│   ├── raporlar/         # Raporlama
│   └── ...
├── components/            # React bileşenleri
│   ├── ui/               # Shadcn/ui bileşenleri
│   └── ...
├── electron/             # Electron ana dosyaları
│   ├── main.js          # Ana Electron süreci
│   ├── preload.js       # Güvenli API köprüsü
│   └── renderer.js      # Renderer yardımcıları
├── lib/                  # Yardımcı kütüphaneler
│   ├── db.ts            # Veri yönetimi (localStorage)
│   └── utils.ts         # Genel yardımcılar
└── public/              # Statik dosyalar
```

## 🔧 Electron Özellikleri

### Menü Sistemi
- **Dosya**: Yeni işlem, rapor alma, çıkış
- **Düzenle**: Standart düzenleme komutları
- **Görünüm**: Yeniden yükleme, geliştirici araçları
- **Pencere**: Pencere yönetimi
- **Yardım**: Uygulama hakkında bilgi

### Güvenlik
- Context isolation etkin
- Node integration devre dışı
- Güvenli preload script kullanımı

### Platform Desteği
- **Windows**: NSIS installer
- **macOS**: DMG paketi, Apple Silicon desteği
- **Linux**: AppImage formatı

## 📊 Veri Yönetimi

Uygulama şu anda localStorage kullanarak verileri saklar:
- **Ürünler**: Telefon ve aksesuar bilgileri
- **İşlemler**: Satış ve alış kayıtları
- **Müşteriler**: Müşteri bilgileri ve geçmiş

### Gelecek Geliştirmeler
- SQLite veritabanı entegrasyonu
- Veri yedekleme ve geri yükleme
- Bulut senkronizasyonu

## 🎨 UI/UX

- **Tasarım Sistemi**: Shadcn/ui bileşenleri
- **Stil**: Tailwind CSS
- **Tema**: Light/Dark mode desteği
- **Responsive**: Farklı ekran boyutları için optimize

## 🔄 Geliştirme Komutları

```bash
# Geliştirme modu (web)
npm run dev

# Production build
npm run build

# Electron geliştirme modu
npm run electron-dev

# Electron production
npm run electron

# Dağıtım paketi
npm run dist

# Linting
npm run lint
```

## 🐛 Sorun Giderme

### Electron uygulaması açılmıyor
- Node.js sürümünüzü kontrol edin (18+)
- `npm install --legacy-peer-deps` ile bağımlılıkları yeniden yükleyin

### Build hatası
- `out/` klasörünü silin ve tekrar build edin
- TypeScript hatalarını kontrol edin

### Performans sorunları
- Geliştirici araçlarını kapatın (production modda)
- Büyük veri setleri için sayfalama kullanın

## 📝 Lisans

Bu proje özel kullanım için geliştirilmiştir.

## 🤝 Katkıda Bulunma

1. Fork edin
2. Feature branch oluşturun (`git checkout -b feature/amazing-feature`)
3. Commit edin (`git commit -m 'Add amazing feature'`)
4. Push edin (`git push origin feature/amazing-feature`)
5. Pull Request oluşturun

## 📞 İletişim

Sorularınız için lütfen iletişime geçin.
