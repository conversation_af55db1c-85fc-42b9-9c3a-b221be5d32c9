# 🔗 SQLite Veritabanı Bağlantı Kılavuzu

## 📍 Veritabanı Konumu

### macOS (Sizin Sisteminiz):
```
/Users/<USER>/Library/Application Support/telefoncu-takip-electron/telefoncu-takip.db
```

### Windows:
```
%APPDATA%\telefoncu-takip-electron\telefoncu-takip.db
```

### Linux:
```
~/.config/telefoncu-takip-electron/telefoncu-takip.db
```

## 🔧 Bağlantı Yöntemleri

### 1. SQLite Command Line (Önerilen)

#### Veritabanına Bağlan:
```bash
sqlite3 "/Users/<USER>/Library/Application Support/telefoncu-takip-electron/telefoncu-takip.db"
```

#### Tabloları Listele:
```bash
sqlite3 "/Users/<USER>/Library/Application Support/telefoncu-takip-electron/telefoncu-takip.db" ".tables"
```

#### Şemayı Görüntüle:
```bash
sqlite3 "/Users/<USER>/Library/Application Support/telefoncu-takip-electron/telefoncu-takip.db" ".schema"
```

### 2. Hızlı Sorgular

#### Ürün Sayısı:
```bash
sqlite3 "/Users/<USER>/Library/Application Support/telefoncu-takip-electron/telefoncu-takip.db" "SELECT COUNT(*) as urun_sayisi FROM urunler;"
```

#### Tüm Ürünleri Listele:
```bash
sqlite3 "/Users/<USER>/Library/Application Support/telefoncu-takip-electron/telefoncu-takip.db" -header -column "SELECT id, ad, kategori, alis_fiyati, satis_fiyati, stok FROM urunler;"
```

#### Son İşlemleri Görüntüle:
```bash
sqlite3 "/Users/<USER>/Library/Application Support/telefoncu-takip-electron/telefoncu-takip.db" -header -column "SELECT id, tarih, urun_adi, satis_fiyati, musteri_adi FROM islemler ORDER BY id DESC LIMIT 5;"
```

#### Kritik Stok Durumu:
```bash
sqlite3 "/Users/<USER>/Library/Application Support/telefoncu-takip-electron/telefoncu-takip.db" -header -column "SELECT ad, stok, kategori FROM urunler WHERE stok <= 5 ORDER BY stok ASC;"
```

### 3. GUI Araçları

#### DB Browser for SQLite (Ücretsiz):
1. [DB Browser for SQLite](https://sqlitebrowser.org/) indirin
2. Uygulamayı açın
3. "Open Database" ile veritabanı dosyasını seçin
4. Görsel arayüzle verileri yönetin

#### TablePlus (Ücretli):
1. [TablePlus](https://tableplus.com/) indirin
2. "Create a new connection" → SQLite
3. Database path'e dosya yolunu girin
4. Connect'e tıklayın

## 📊 Mevcut Veri Durumu

### Tablolar:
- **urunler**: 10 kayıt (Telefonlar ve aksesuarlar)
- **islemler**: 2 kayıt (Örnek satış işlemleri)
- **musteriler**: 0 kayıt (Henüz müşteri eklenmemiş)

### Örnek Ürünler:
1. iPhone 13 - 18,000₺ → 22,000₺ (5 adet)
2. iPhone 13 Pro - 25,000₺ → 30,000₺ (3 adet)
3. Galaxy S22 - 15,000₺ → 18,500₺ (7 adet)
4. Galaxy S22 Ultra - 22,000₺ → 26,000₺ (2 adet)
5. Redmi Note 11 - 6,000₺ → 7,500₺ (10 adet)

## 🔍 Yararlı SQL Sorguları

### Toplam Kar Hesaplama:
```sql
SELECT 
  SUM(kar) as toplam_kar,
  COUNT(*) as islem_sayisi,
  AVG(kar) as ortalama_kar
FROM islemler;
```

### Kategori Bazlı Satış:
```sql
SELECT 
  kategori,
  COUNT(*) as islem_sayisi,
  SUM(satis_fiyati) as toplam_satis,
  SUM(kar) as toplam_kar
FROM islemler 
GROUP BY kategori;
```

### En Çok Satan Ürünler:
```sql
SELECT 
  urun_adi,
  COUNT(*) as satis_adedi,
  SUM(satis_fiyati) as toplam_gelir
FROM islemler 
GROUP BY urun_adi 
ORDER BY satis_adedi DESC;
```

### Stok Durumu Raporu:
```sql
SELECT 
  kategori,
  COUNT(*) as urun_sayisi,
  SUM(stok) as toplam_stok,
  AVG(stok) as ortalama_stok
FROM urunler 
GROUP BY kategori;
```

## 🛠️ NPM Komutları

### Veritabanı Bilgileri:
```bash
npm run db:info
```

### İstatistikler:
```bash
npm run db:stats
```

### Yedekleme:
```bash
npm run db:backup
```

### SQLite Shell:
```bash
npm run db:shell
```

## 🔒 Güvenlik Notları

1. **Yedekleme**: Düzenli olarak veritabanını yedekleyin
2. **İzinler**: Veritabanı dosyasının izinlerini kontrol edin
3. **Şifreleme**: Hassas veriler için şifreleme düşünün
4. **Erişim**: Sadece gerekli uygulamaların erişimine izin verin

## 🚨 Sorun Giderme

### Veritabanı Bulunamıyor:
```bash
# Önce Electron uygulamasını çalıştırın
npm run electron
```

### İzin Hatası:
```bash
# Dosya izinlerini düzeltin
chmod 644 "/Users/<USER>/Library/Application Support/telefoncu-takip-electron/telefoncu-takip.db"
```

### Bozuk Veritabanı:
```bash
# Veritabanını onar
sqlite3 "/Users/<USER>/Library/Application Support/telefoncu-takip-electron/telefoncu-takip.db" ".recover"
```

## 📞 Destek

Veritabanı ile ilgili sorunlar için:
1. Bu kılavuzu kontrol edin
2. Electron uygulamasının loglarını inceleyin
3. SQLite command line ile bağlantıyı test edin
4. Gerekirse yedekten geri yükleyin
